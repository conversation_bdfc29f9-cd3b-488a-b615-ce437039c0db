# 🗄️ Database Files

This directory contains all database-related files for the Tap2Go platform, organized by database type and purpose.

## 📁 Directory Structure

```
database/
├── README.md                           # This file
└── supabase/                          # Supabase PostgreSQL database files
    ├── SETUP_GUIDE.md                 # Complete setup and usage guide
    ├── schema/                        # Database schema definitions
    │   ├── supabase-cms-schema.sql    # CMS tables and structure
    │   └── complete-cms-setup.sql     # Complete CMS setup with security
    ├── policies/                      # Row Level Security policies
    │   └── supabase-cms-policies.sql  # RLS policies for CMS
    └── sample-data/                   # Sample/seed data
        └── supabase-sample-data.sql   # Sample CMS content
```

## 🔧 Supabase Database Files

### Setup Guide (`SETUP_GUIDE.md`)
Complete automated setup and usage guide for the Supabase CMS including:
- One-command automated setup
- Manual setup instructions
- Code usage examples
- Troubleshooting guide
- Integration examples

### Schema Files (`schema/`)
Contains SQL files that define the database structure, tables, indexes, and functions.

- **`supabase-cms-schema.sql`**: WordPress-style CMS schema including:
  - Blog posts and static pages
  - Categories and tags (taxonomy)
  - Media library
  - Full-text search capabilities
  - Soft delete functionality
  - Performance indexes

- **`complete-cms-setup.sql`**: Complete CMS setup with advanced features:
  - All schema tables with dynamic column addition
  - WordPress-style helper functions
  - Professional security configuration
  - Comprehensive permissions setup
  - Production-ready optimization

### Policy Files (`policies/`)
Contains Row Level Security (RLS) policies for data access control.

- **`supabase-cms-policies.sql`**: Security policies for:
  - Public read access to published content
  - Service role admin access
  - Content visibility rules

### Sample Data (`sample-data/`)
Contains seed data for development and testing.

- **`supabase-sample-data.sql`**: Sample content including:
  - Categories and tags
  - Blog posts with rich content
  - Static pages (About, Contact, Privacy)
  - Relationship mappings

## 🚀 Usage Instructions

### 1. Setting up the Schema
Run the schema file first to create all tables and functions:
```sql
-- In Supabase SQL Editor
\i database/supabase/schema/supabase-cms-schema.sql
```

### 2. Applying Security Policies
Run the policies file to enable Row Level Security:
```sql
-- In Supabase SQL Editor
\i database/supabase/policies/supabase-cms-policies.sql
```

### 3. Loading Sample Data (Optional)
Load sample data for development/testing:
```sql
-- In Supabase SQL Editor
\i database/supabase/sample-data/supabase-sample-data.sql
```

## 📋 Execution Order

**Important**: Always run files in this order:
1. Schema files (creates structure)
2. Policy files (applies security)
3. Sample data files (populates content)

## 🔗 Related Documentation

- [Supabase CMS Setup Guide](./supabase/SETUP_GUIDE.md) - **Start here for Supabase setup**
- [Caching Architecture Blueprint](../docs/architecture/CACHING_BLUEPRINT.md) - **Enterprise caching strategies**
- [Database Setup Guide](../docs/database/DATABASE_SETUP.md)
- [Supabase Security Best Practices](../docs/database/SUPABASE_SECURITY_BEST_PRACTICES.md)
- [Setup Guide](../docs/setup/SETUP_GUIDE_CURRENT.md)

## 🛠️ Development Notes

- All SQL files use `IF NOT EXISTS` clauses for safe re-execution
- Schema includes WordPress-style soft delete functionality
- Full-text search is enabled with PostgreSQL's `tsvector`
- Indexes are optimized for common CMS query patterns
- RLS policies ensure proper content visibility

## 🔄 Migration Strategy

When updating database schema:
1. Create new migration files in appropriate subdirectories
2. Test migrations on development environment first
3. Document any breaking changes
4. Update related application code before deploying
